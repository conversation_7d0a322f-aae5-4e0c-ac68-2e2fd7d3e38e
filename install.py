#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装脚本
自动检查和安装依赖
"""

import subprocess
import sys
import os
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✓ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_admin_privileges():
    """检查管理员权限"""
    try:
        if platform.system() == "Windows":
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                print("警告: 未检测到管理员权限")
                print("建议以管理员身份运行以获得完整功能")
                return False
            else:
                print("✓ 管理员权限检查通过")
                return True
        else:
            # Linux/Mac检查
            if os.geteuid() != 0:
                print("警告: 未检测到root权限")
                print("某些功能可能需要sudo权限")
                return False
            else:
                print("✓ Root权限检查通过")
                return True
    except Exception as e:
        print(f"权限检查失败: {e}")
        return False

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, check=True)
        
        print(f"✓ {package} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败:")
        print(f"  错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ 安装 {package} 时发生错误: {e}")
        return False

def check_and_install_dependencies():
    """检查并安装依赖包"""
    dependencies = [
        "scapy==2.5.0",
        "netifaces==0.11.0", 
        "psutil==5.9.8"
    ]
    
    print("检查依赖包...")
    
    failed_packages = []
    
    for package in dependencies:
        package_name = package.split("==")[0]
        try:
            __import__(package_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            print(f"- {package_name} 未安装，正在安装...")
            if not install_package(package):
                failed_packages.append(package)
    
    if failed_packages:
        print(f"\n以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    
    print("✓ 所有依赖包检查完成")
    return True

def check_network_tools():
    """检查网络工具"""
    tools_status = {}
    
    if platform.system() == "Windows":
        # Windows工具检查
        tools = ["netsh", "ping"]
        for tool in tools:
            try:
                result = subprocess.run([tool], capture_output=True, text=True)
                tools_status[tool] = True
                print(f"✓ {tool} 可用")
            except FileNotFoundError:
                tools_status[tool] = False
                print(f"✗ {tool} 不可用")
    
    elif platform.system() == "Linux":
        # Linux工具检查
        tools = ["tc", "iptables", "ping"]
        for tool in tools:
            try:
                result = subprocess.run(["which", tool], capture_output=True, text=True)
                if result.returncode == 0:
                    tools_status[tool] = True
                    print(f"✓ {tool} 可用")
                else:
                    tools_status[tool] = False
                    print(f"✗ {tool} 不可用")
            except Exception:
                tools_status[tool] = False
                print(f"✗ {tool} 检查失败")
    
    return tools_status

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    try:
        if platform.system() == "Windows":
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "局域网设备管理工具.lnk")
            target = os.path.join(os.getcwd(), "start.bat")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = target
            shortcut.WorkingDirectory = os.getcwd()
            shortcut.IconLocation = target
            shortcut.save()
            
            print("✓ 桌面快捷方式创建成功")
            return True
            
    except Exception as e:
        print(f"创建桌面快捷方式失败: {e}")
        return False

def main():
    """主安装函数"""
    print("=" * 50)
    print("局域网设备管理工具 - 安装脚本")
    print("=" * 50)
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return False
    
    print()
    
    # 检查管理员权限
    check_admin_privileges()
    print()
    
    # 安装依赖
    if not check_and_install_dependencies():
        print("\n依赖安装失败，请手动安装后重试")
        input("按回车键退出...")
        return False
    
    print()
    
    # 检查网络工具
    print("检查系统网络工具...")
    tools_status = check_network_tools()
    print()
    
    # 创建配置文件
    if not os.path.exists("config.json"):
        config = {
            "blocked_devices": [],
            "settings": {
                "scan_timeout": 2,
                "arp_interval": 2,
                "auto_save": True
            }
        }
        
        try:
            import json
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print("✓ 配置文件创建成功")
        except Exception as e:
            print(f"配置文件创建失败: {e}")
    
    # 创建桌面快捷方式
    if platform.system() == "Windows":
        try:
            create_desktop_shortcut()
        except Exception as e:
            print(f"快捷方式创建失败: {e}")
    
    print()
    print("=" * 50)
    print("安装完成!")
    print("=" * 50)
    print()
    print("使用说明:")
    print("1. 双击 start.bat 启动程序")
    print("2. 或者运行: python network_manager.py")
    print("3. 建议以管理员身份运行以获得完整功能")
    print()
    print("注意事项:")
    print("- 此工具仅供学习和合法网络管理使用")
    print("- 请确保你有相应的网络管理权限")
    print("- 不当使用可能违反相关法律法规")
    print()
    
    input("按回车键退出...")
    return True

if __name__ == "__main__":
    main()
