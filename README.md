# 局域网设备管理工具

一个功能强大的局域网设备管理工具，可以扫描、屏蔽和限制局域网内设备的网络访问。

## 功能特性

- 🔍 **设备扫描**: 自动扫描局域网内的所有设备
- 🚫 **设备屏蔽**: 使用ARP欺骗技术断开指定设备的网络连接
- 🌐 **网速限制**: 限制指定设备的上传和下载速度
- 📊 **实时监控**: 显示设备状态和操作日志
- 💾 **本地存储**: 设备信息和操作日志保存到本地文件
- 📋 **日志查看**: 专用的日志查看器，支持过滤和导出

## 系统要求

- Windows 10/11 (推荐)
- Python 3.7 或更高版本
- 管理员权限 (某些功能需要)
- 无需数据库，使用本地文件存储

## 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd wifi-manager
   ```

2. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **以管理员身份运行**
   - 右键点击 `start.bat`
   - 选择 "以管理员身份运行"

## 使用方法

### 1. 启动程序
- 双击 `start.bat` 或以管理员身份运行
- 程序会自动检测网络配置并显示本机信息

### 2. 扫描设备
- 点击 "扫描设备" 按钮
- 程序会扫描当前局域网内的所有设备
- 扫描结果显示在设备列表中

### 3. 屏蔽设备
- 在设备列表中选择要屏蔽的设备
- 点击 "屏蔽选中设备" 按钮
- 被屏蔽的设备将无法访问网络

### 4. 解除屏蔽
- 选择已屏蔽的设备
- 点击 "解除屏蔽" 按钮
- 设备网络访问将恢复正常

### 5. 限制网速
- 选择要限制的设备
- 点击 "限制网速" 按钮
- 设置上传和下载速度限制

## 技术原理

### ARP欺骗原理
程序使用ARP欺骗技术来屏蔽设备：
1. 向目标设备发送虚假的网关ARP响应
2. 向网关发送虚假的目标设备ARP响应
3. 导致目标设备的网络流量被重定向到本机
4. 本机不转发这些流量，从而实现屏蔽效果

### 网络扫描
使用ARP广播请求扫描局域网：
1. 发送ARP请求到整个网段
2. 收集响应的设备信息
3. 解析设备名称和厂商信息

## 安全警告

⚠️ **重要提示**:
- 此工具仅供学习和合法的网络管理使用
- 请勿在未经授权的网络上使用
- 使用前请确保你有相应的网络管理权限
- 不当使用可能违反相关法律法规

## 故障排除

### 常见问题

1. **程序无法启动**
   - 确保以管理员身份运行
   - 检查Python版本是否为3.7+
   - 确认所有依赖包已正确安装

2. **扫描不到设备**
   - 检查网络连接
   - 确认防火墙设置
   - 尝试重新启动程序

3. **屏蔽功能无效**
   - 确保以管理员身份运行
   - 检查目标设备是否支持ARP
   - 某些设备可能有ARP防护机制

4. **网速限制不生效**
   - 此功能需要额外的系统权限
   - 可能需要安装额外的网络工具
   - 建议使用专业的QoS工具

### 依赖包说明

- **scapy**: 网络包处理库，用于ARP操作
- **netifaces**: 网络接口信息获取
- **psutil**: 系统和进程信息获取

### 数据文件说明

程序会在运行目录下创建以下文件：
- **config.json**: 程序配置文件
- **devices.json**: 设备历史信息
- **devices.csv**: 设备信息CSV格式
- **operation.log**: 操作日志文件
- **detailed_log.json**: 详细日志JSON格式

## 开发说明

### 项目结构
```
wifi-manager/
├── network_manager.py    # 主程序文件
├── log_viewer.py        # 日志查看器
├── traffic_control.py   # 流量控制模块
├── install.py          # 安装脚本
├── requirements.txt    # Python依赖
├── start.bat          # Windows启动脚本
├── README.md          # 说明文档
├── 使用指南.md        # 详细使用指南
├── config.json        # 配置文件(自动生成)
├── devices.json       # 设备信息(自动生成)
├── devices.csv        # 设备CSV文件(自动生成)
├── operation.log      # 操作日志(自动生成)
└── detailed_log.json  # 详细日志(自动生成)
```

### 主要类和方法

- `NetworkManager`: 主程序类
  - `scan_network()`: 扫描网络设备
  - `block_device()`: 屏蔽设备
  - `unblock_device()`: 解除屏蔽
  - `start_arp_spoofing()`: 开始ARP欺骗
  - `stop_arp_spoofing()`: 停止ARP欺骗

## 许可证

本项目仅供学习和研究使用。使用者需要自行承担使用风险和法律责任。

## 贡献

欢迎提交问题报告和功能建议。

---

**免责声明**: 本工具仅供学习和合法的网络管理使用。使用者应当遵守当地法律法规，不得用于非法目的。开发者不承担任何因使用本工具而产生的法律责任。
