@echo off
title Network Device Manager

echo Starting Network Device Manager...
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo WARNING: Please run as administrator for full functionality
    echo.
)

REM Try to start the main program
python network_manager.py
if %errorLevel% neq 0 (
    echo.
    echo Failed to start the program.
    echo Please check:
    echo 1. Python is installed
    echo 2. Dependencies are installed: pip install scapy netifaces psutil
    echo 3. Run as administrator
    echo.
    echo You can also run: python check_install.py
)

pause
