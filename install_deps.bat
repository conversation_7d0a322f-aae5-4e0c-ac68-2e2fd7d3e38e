@echo off
chcp 65001 >nul
echo Installing Network Manager Dependencies
echo =====================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.7+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip
echo.

REM Install dependencies one by one
echo Installing scapy...
pip install scapy==2.5.0
if %errorLevel% neq 0 (
    echo Failed to install scapy
    echo Trying alternative installation...
    pip install scapy
)
echo.

echo Installing netifaces...
pip install netifaces==0.11.0
if %errorLevel% neq 0 (
    echo Failed to install netifaces
    echo Trying alternative installation...
    pip install netifaces
)
echo.

echo Installing psutil...
pip install psutil==5.9.8
if %errorLevel% neq 0 (
    echo Failed to install psutil
    echo Trying alternative installation...
    pip install psutil
)
echo.

REM Verify installations
echo Verifying installations...
python -c "import scapy; print('scapy: OK')" 2>nul || echo scapy: FAILED
python -c "import netifaces; print('netifaces: OK')" 2>nul || echo netifaces: FAILED
python -c "import psutil; print('psutil: OK')" 2>nul || echo psutil: FAILED
echo.

echo Installation complete!
echo You can now run the network manager.
echo.
pause
