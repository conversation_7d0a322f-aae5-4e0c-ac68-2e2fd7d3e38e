@echo off
chcp 65001 >nul
echo Installing Minimal Dependencies for Network Manager
echo ==================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.7+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip
echo.

REM Install only essential dependencies
echo Installing scapy...
pip install scapy
if %errorLevel% neq 0 (
    echo Failed to install scapy
    pause
    exit /b 1
)
echo.

echo Installing psutil...
pip install psutil
if %errorLevel% neq 0 (
    echo Failed to install psutil
    pause
    exit /b 1
)
echo.

REM Try to install netifaces (optional)
echo Trying to install netifaces (optional)...
pip install netifaces
if %errorLevel% neq 0 (
    echo netifaces installation failed - this is OK, program will use alternative method
)
echo.

REM Verify installations
echo Verifying installations...
python -c "import scapy; print('scapy: OK')" 2>nul || echo scapy: FAILED
python -c "import psutil; print('psutil: OK')" 2>nul || echo psutil: FAILED
python -c "import netifaces; print('netifaces: OK')" 2>nul || echo netifaces: Not available (will use alternative)
echo.

echo Installation complete!
echo You can now run: python network_manager.py
echo.
pause
