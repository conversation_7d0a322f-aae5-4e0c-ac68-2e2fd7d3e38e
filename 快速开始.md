# 快速开始指南

## 🚀 5分钟快速上手

### 第一步：检查环境
```bash
# 检查Python和依赖
python check_install.py
```

### 第二步：安装依赖
```bash
# 方法1：使用批处理脚本（Windows推荐）
双击 install_deps.bat

# 方法2：手动安装
pip install scapy netifaces psutil

# 方法3：使用requirements.txt
pip install -r requirements.txt
```

### 第三步：启动程序
```bash
# 方法1：简单启动（推荐）
右键点击 run.bat → "以管理员身份运行"

# 方法2：完整启动脚本
右键点击 start.bat → "以管理员身份运行"

# 方法3：命令行启动
python network_manager.py
```

### 第三步：基本操作

1. **扫描设备**
   - 点击 "扫描设备" 按钮
   - 等待扫描完成，查看发现的设备

2. **屏蔽设备**
   - 在设备列表中选择要屏蔽的设备
   - 点击 "屏蔽选中设备"
   - 设备状态变为 "已屏蔽"

3. **解除屏蔽**
   - 选择已屏蔽的设备
   - 点击 "解除屏蔽"
   - 设备恢复正常网络访问

4. **查看日志**
   - 启动脚本选择 "2. 日志查看器"
   - 或直接运行：`python log_viewer.py`

## 📁 生成的文件说明

程序运行后会自动创建以下文件：

| 文件名 | 说明 | 格式 |
|--------|------|------|
| `config.json` | 程序配置 | JSON |
| `devices.json` | 设备历史信息 | JSON |
| `devices.csv` | 设备信息表格 | CSV |
| `operation.log` | 操作日志 | 文本 |
| `detailed_log.json` | 详细日志 | JSON |

## 🔧 常用操作

### 查看设备历史
```bash
# 打开devices.csv文件（可用Excel打开）
# 或使用日志查看器查看详细信息
```

### 导出日志
1. 运行日志查看器：`python log_viewer.py`
2. 点击 "导出CSV" 按钮
3. 选择保存位置

### 清理日志
1. 运行日志查看器
2. 点击 "清空日志" 按钮
3. 确认删除

### 备份数据
```bash
# 备份所有数据文件
copy *.json backup/
copy *.csv backup/
copy *.log backup/
```

## ⚠️ 注意事项

1. **管理员权限**：程序需要管理员权限才能正常工作
2. **网络影响**：ARP欺骗可能影响网络稳定性，请谨慎使用
3. **合法使用**：仅在自己管理的网络中使用
4. **数据备份**：重要数据请定期备份

## 🐛 常见问题

### Q: 出现 "ModuleNotFoundError: No module named 'scapy'"？
A: 依赖包未安装，解决方法：
```bash
# 运行安装脚本
python check_install.py

# 或手动安装
pip install scapy netifaces psutil
```

### Q: 批处理文件出现乱码？
A: 使用简化的启动脚本：
```bash
# 右键以管理员身份运行
run.bat
```

### Q: Python未找到？
A:
1. 确保Python已安装：https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"
3. 重启命令提示符

### Q: 程序无法启动？
A: 按顺序检查：
1. 运行 `python check_install.py` 检查环境
2. 确保以管理员身份运行
3. 检查防火墙设置

### Q: 扫描不到设备？
A: 检查网络连接，确认防火墙设置

### Q: 屏蔽功能无效？
A: 确保管理员权限，检查目标设备ARP支持

### Q: 如何恢复被误屏蔽的设备？
A: 选择设备点击"解除屏蔽"，或重启网络设备

## 📞 获取帮助

如果遇到问题：
1. 查看操作日志了解错误信息
2. 检查 `operation.log` 文件
3. 使用日志查看器分析问题
4. 重启程序或网络设备

---

**提示**：首次使用建议在测试环境中验证功能，确保理解工具的工作原理后再在生产环境使用。
