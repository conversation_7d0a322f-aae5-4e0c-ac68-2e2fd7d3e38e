#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入脚本
验证所有必需的模块是否可以正常导入
"""

import sys

def test_import(module_name, optional=False):
    """测试导入模块"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - OK")
        return True
    except ImportError as e:
        if optional:
            print(f"⚠️  {module_name} - Optional (not available): {e}")
            return True
        else:
            print(f"❌ {module_name} - FAILED: {e}")
            return False

def main():
    print("测试模块导入...")
    print("=" * 40)
    
    success = True
    
    # 测试必需的模块
    print("必需模块:")
    success &= test_import("tkinter")
    success &= test_import("threading")
    success &= test_import("time")
    success &= test_import("subprocess")
    success &= test_import("socket")
    success &= test_import("scapy")
    success &= test_import("psutil")
    success &= test_import("json")
    success &= test_import("os")
    success &= test_import("csv")
    success &= test_import("logging")
    success &= test_import("datetime")
    
    print("\n可选模块:")
    test_import("netifaces", optional=True)
    
    print("\n" + "=" * 40)
    if success:
        print("✅ 所有必需模块导入成功！")
        print("程序应该可以正常运行。")
    else:
        print("❌ 部分必需模块导入失败！")
        print("请安装缺失的模块。")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        sys.exit(1)
