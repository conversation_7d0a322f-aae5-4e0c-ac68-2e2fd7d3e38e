#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
处理设备信息和操作日志的数据库操作
"""

import mysql.connector
from mysql.connector import Error
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    def __init__(self, host='localhost', database='network_manager', 
                 user='root', password='', port=3306):
        """
        初始化数据库连接
        
        Args:
            host: 数据库主机
            database: 数据库名
            user: 用户名
            password: 密码
            port: 端口
        """
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.connection = None
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                port=self.port,
                charset='utf8mb4',
                autocommit=True
            )
            
            if self.connection.is_connected():
                self.logger.info("数据库连接成功")
                return True
                
        except Error as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            self.logger.info("数据库连接已关闭")
    
    def execute_query(self, query: str, params: tuple = None) -> Optional[List]:
        """执行查询语句"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return None
            
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                cursor.close()
                return result
            else:
                self.connection.commit()
                cursor.close()
                return []
                
        except Error as e:
            self.logger.error(f"查询执行失败: {e}")
            return None
    
    def update_device(self, ip_address: str, mac_address: str, 
                     device_name: str = None, vendor: str = None) -> bool:
        """更新或插入设备信息"""
        try:
            query = """
            INSERT INTO devices (ip_address, mac_address, device_name, vendor)
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                device_name = IFNULL(%s, device_name),
                vendor = IFNULL(%s, vendor),
                last_seen = CURRENT_TIMESTAMP
            """
            params = (ip_address, mac_address, device_name, vendor, 
                     device_name, vendor)
            
            result = self.execute_query(query, params)
            return result is not None
            
        except Exception as e:
            self.logger.error(f"更新设备信息失败: {e}")
            return False
    
    def log_operation(self, device_ip: str, device_mac: str = None,
                     operation_type: str = 'other', operation_detail: str = None,
                     result: str = 'success', error_message: str = None,
                     operator: str = 'system') -> bool:
        """记录操作日志"""
        try:
            query = """
            INSERT INTO operation_logs 
            (device_ip, device_mac, operation_type, operation_detail, 
             result, error_message, operator)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (device_ip, device_mac, operation_type, operation_detail,
                     result, error_message, operator)
            
            result = self.execute_query(query, params)
            return result is not None
            
        except Exception as e:
            self.logger.error(f"记录操作日志失败: {e}")
            return False
    
    def get_devices(self, active_only: bool = False) -> List[Dict]:
        """获取设备列表"""
        try:
            if active_only:
                query = """
                SELECT * FROM devices 
                WHERE last_seen >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ORDER BY last_seen DESC
                """
            else:
                query = "SELECT * FROM devices ORDER BY last_seen DESC"
            
            result = self.execute_query(query)
            return result if result else []
            
        except Exception as e:
            self.logger.error(f"获取设备列表失败: {e}")
            return []
    
    def get_blocked_devices(self) -> List[Dict]:
        """获取被屏蔽的设备"""
        try:
            query = "SELECT * FROM blocked_devices"
            result = self.execute_query(query)
            return result if result else []
            
        except Exception as e:
            self.logger.error(f"获取屏蔽设备列表失败: {e}")
            return []
    
    def set_device_blocked(self, ip_address: str, blocked: bool = True) -> bool:
        """设置设备屏蔽状态"""
        try:
            query = """
            UPDATE devices 
            SET is_blocked = %s 
            WHERE ip_address = %s
            """
            params = (blocked, ip_address)
            
            result = self.execute_query(query, params)
            
            # 记录操作日志
            operation_type = 'block' if blocked else 'unblock'
            operation_detail = f"设备{'屏蔽' if blocked else '解除屏蔽'}"
            self.log_operation(ip_address, operation_type=operation_type,
                             operation_detail=operation_detail)
            
            return result is not None
            
        except Exception as e:
            self.logger.error(f"设置设备屏蔽状态失败: {e}")
            return False
    
    def set_device_speed_limit(self, ip_address: str, download_limit: int = 0,
                              upload_limit: int = 0) -> bool:
        """设置设备速度限制"""
        try:
            is_limited = download_limit > 0 or upload_limit > 0
            
            query = """
            UPDATE devices 
            SET is_speed_limited = %s, download_limit = %s, upload_limit = %s
            WHERE ip_address = %s
            """
            params = (is_limited, download_limit, upload_limit, ip_address)
            
            result = self.execute_query(query, params)
            
            # 记录操作日志
            if is_limited:
                operation_detail = f"设置速度限制: 下载{download_limit}KB/s, 上传{upload_limit}KB/s"
                operation_type = 'limit_speed'
            else:
                operation_detail = "移除速度限制"
                operation_type = 'remove_limit'
            
            self.log_operation(ip_address, operation_type=operation_type,
                             operation_detail=operation_detail)
            
            return result is not None
            
        except Exception as e:
            self.logger.error(f"设置设备速度限制失败: {e}")
            return False
    
    def record_scan(self, network_segment: str, devices_found: int,
                   scan_duration: float, gateway_ip: str = None,
                   local_ip: str = None, notes: str = None) -> bool:
        """记录网络扫描"""
        try:
            query = """
            INSERT INTO scan_records 
            (network_segment, devices_found, scan_duration, gateway_ip, local_ip, notes)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (network_segment, devices_found, scan_duration,
                     gateway_ip, local_ip, notes)
            
            result = self.execute_query(query, params)
            return result is not None
            
        except Exception as e:
            self.logger.error(f"记录扫描信息失败: {e}")
            return False
    
    def get_operation_logs(self, device_ip: str = None, 
                          operation_type: str = None,
                          days: int = 7) -> List[Dict]:
        """获取操作日志"""
        try:
            conditions = ["created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)"]
            params = [days]
            
            if device_ip:
                conditions.append("device_ip = %s")
                params.append(device_ip)
            
            if operation_type:
                conditions.append("operation_type = %s")
                params.append(operation_type)
            
            query = f"""
            SELECT * FROM operation_logs 
            WHERE {' AND '.join(conditions)}
            ORDER BY created_at DESC
            """
            
            result = self.execute_query(query, tuple(params))
            return result if result else []
            
        except Exception as e:
            self.logger.error(f"获取操作日志失败: {e}")
            return []
    
    def get_config(self, config_key: str) -> Optional[str]:
        """获取系统配置"""
        try:
            query = "SELECT config_value FROM system_config WHERE config_key = %s"
            result = self.execute_query(query, (config_key,))
            
            if result and len(result) > 0:
                return result[0]['config_value']
            return None
            
        except Exception as e:
            self.logger.error(f"获取配置失败: {e}")
            return None
    
    def set_config(self, config_key: str, config_value: str,
                   config_type: str = 'string', description: str = None) -> bool:
        """设置系统配置"""
        try:
            query = """
            INSERT INTO system_config (config_key, config_value, config_type, description)
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                config_value = VALUES(config_value),
                config_type = VALUES(config_type),
                description = IFNULL(VALUES(description), description),
                updated_at = CURRENT_TIMESTAMP
            """
            params = (config_key, config_value, config_type, description)
            
            result = self.execute_query(query, params)
            return result is not None
            
        except Exception as e:
            self.logger.error(f"设置配置失败: {e}")
            return False
    
    def cleanup_old_logs(self, retention_days: int = None) -> int:
        """清理旧日志"""
        try:
            if retention_days is None:
                retention_days = int(self.get_config('log_retention_days') or '30')
            
            # 清理操作日志
            query1 = """
            DELETE FROM operation_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            
            # 清理流量统计
            query2 = """
            DELETE FROM traffic_stats 
            WHERE record_time < DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            
            # 清理扫描记录
            query3 = """
            DELETE FROM scan_records 
            WHERE scan_time < DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            
            total_deleted = 0
            for query in [query1, query2, query3]:
                self.execute_query(query, (retention_days,))
                # 这里应该获取实际删除的行数，但简化处理
                total_deleted += 1
            
            self.logger.info(f"清理了 {retention_days} 天前的旧日志")
            return total_deleted
            
        except Exception as e:
            self.logger.error(f"清理旧日志失败: {e}")
            return 0
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        try:
            stats = {}
            
            # 设备统计
            result = self.execute_query("SELECT COUNT(*) as total FROM devices")
            stats['total_devices'] = result[0]['total'] if result else 0
            
            result = self.execute_query("SELECT COUNT(*) as active FROM active_devices")
            stats['active_devices'] = result[0]['active'] if result else 0
            
            result = self.execute_query("SELECT COUNT(*) as blocked FROM devices WHERE is_blocked = TRUE")
            stats['blocked_devices'] = result[0]['blocked'] if result else 0
            
            result = self.execute_query("SELECT COUNT(*) as limited FROM devices WHERE is_speed_limited = TRUE")
            stats['speed_limited_devices'] = result[0]['limited'] if result else 0
            
            # 操作统计
            result = self.execute_query("""
                SELECT operation_type, COUNT(*) as count 
                FROM operation_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY operation_type
            """)
            
            stats['operations_24h'] = {row['operation_type']: row['count'] for row in result} if result else {}
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}

# 使用示例
if __name__ == "__main__":
    # 创建数据库管理器实例
    db = DatabaseManager(
        host='localhost',
        database='network_manager',
        user='root',
        password='your_password'
    )
    
    # 连接数据库
    if db.connect():
        # 更新设备信息
        db.update_device('*************', '00:11:22:33:44:55', '测试设备', 'Test Vendor')
        
        # 记录操作日志
        db.log_operation('*************', operation_type='scan', 
                        operation_detail='设备扫描', result='success')
        
        # 获取设备列表
        devices = db.get_devices()
        print(f"发现 {len(devices)} 个设备")
        
        # 获取统计信息
        stats = db.get_statistics()
        print(f"统计信息: {stats}")
        
        # 关闭连接
        db.disconnect()
    else:
        print("数据库连接失败")
