-- 局域网设备管理工具数据库脚本
-- 用于记录设备信息和操作日志

-- 创建数据库
CREATE DATABASE IF NOT EXISTS network_manager 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE network_manager;

-- 设备信息表
CREATE TABLE IF NOT EXISTS devices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(15) NOT NULL,
    mac_address VARCHAR(17) NOT NULL,
    device_name VARCHAR(255) DEFAULT '未知设备',
    vendor VARCHAR(255) DEFAULT '未知厂商',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_blocked BOOLEAN DEFAULT FALSE,
    is_speed_limited BOOLEAN DEFAULT FALSE,
    download_limit INT DEFAULT 0 COMMENT '下载限制(KB/s)',
    upload_limit INT DEFAULT 0 COMMENT '上传限制(KB/s)',
    notes TEXT,
    UNIQUE KEY unique_device (ip_address, mac_address),
    INDEX idx_ip (ip_address),
    INDEX idx_mac (mac_address),
    INDEX idx_blocked (is_blocked),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_ip VARCHAR(15) NOT NULL,
    device_mac VARCHAR(17),
    operation_type ENUM('scan', 'block', 'unblock', 'limit_speed', 'remove_limit', 'other') NOT NULL,
    operation_detail TEXT,
    result ENUM('success', 'failed', 'warning') NOT NULL,
    error_message TEXT,
    operator VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_ip (device_ip),
    INDEX idx_operation_type (operation_type),
    INDEX idx_result (result),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 网络扫描记录表
CREATE TABLE IF NOT EXISTS scan_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    network_segment VARCHAR(20) NOT NULL,
    devices_found INT DEFAULT 0,
    scan_duration DECIMAL(5,2) DEFAULT 0.00 COMMENT '扫描耗时(秒)',
    gateway_ip VARCHAR(15),
    local_ip VARCHAR(15),
    notes TEXT,
    INDEX idx_scan_time (scan_time),
    INDEX idx_network (network_segment)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 流量统计表
CREATE TABLE IF NOT EXISTS traffic_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_ip VARCHAR(15) NOT NULL,
    device_mac VARCHAR(17),
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    bytes_sent BIGINT DEFAULT 0,
    bytes_received BIGINT DEFAULT 0,
    packets_sent BIGINT DEFAULT 0,
    packets_received BIGINT DEFAULT 0,
    connection_count INT DEFAULT 0,
    INDEX idx_device_ip (device_ip),
    INDEX idx_record_time (record_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('scan_timeout', '2', 'int', '网络扫描超时时间(秒)'),
('arp_interval', '2', 'int', 'ARP欺骗发送间隔(秒)'),
('auto_save_config', 'true', 'boolean', '是否自动保存配置'),
('log_retention_days', '30', 'int', '日志保留天数'),
('max_concurrent_blocks', '10', 'int', '最大同时屏蔽设备数'),
('enable_traffic_monitoring', 'true', 'boolean', '是否启用流量监控'),
('default_download_limit', '1000', 'int', '默认下载限制(KB/s)'),
('default_upload_limit', '500', 'int', '默认上传限制(KB/s)')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = CURRENT_TIMESTAMP;

-- 创建视图：活跃设备
CREATE OR REPLACE VIEW active_devices AS
SELECT 
    d.*,
    TIMESTAMPDIFF(MINUTE, d.last_seen, NOW()) as minutes_since_last_seen
FROM devices d
WHERE d.last_seen >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY d.last_seen DESC;

-- 创建视图：被屏蔽的设备
CREATE OR REPLACE VIEW blocked_devices AS
SELECT 
    d.*,
    ol.created_at as blocked_at,
    ol.operator as blocked_by
FROM devices d
LEFT JOIN operation_logs ol ON d.ip_address = ol.device_ip 
    AND ol.operation_type = 'block' 
    AND ol.result = 'success'
WHERE d.is_blocked = TRUE
ORDER BY ol.created_at DESC;

-- 创建视图：限速设备
CREATE OR REPLACE VIEW speed_limited_devices AS
SELECT 
    d.*,
    CONCAT(d.download_limit, '/', d.upload_limit) as speed_limit
FROM devices d
WHERE d.is_speed_limited = TRUE
ORDER BY d.last_seen DESC;

-- 创建视图：操作统计
CREATE OR REPLACE VIEW operation_stats AS
SELECT 
    operation_type,
    result,
    COUNT(*) as count,
    DATE(created_at) as operation_date
FROM operation_logs
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY operation_type, result, DATE(created_at)
ORDER BY operation_date DESC, operation_type;

-- 存储过程：记录操作日志
DELIMITER //
CREATE PROCEDURE LogOperation(
    IN p_device_ip VARCHAR(15),
    IN p_device_mac VARCHAR(17),
    IN p_operation_type VARCHAR(20),
    IN p_operation_detail TEXT,
    IN p_result VARCHAR(10),
    IN p_error_message TEXT,
    IN p_operator VARCHAR(100)
)
BEGIN
    INSERT INTO operation_logs (
        device_ip, device_mac, operation_type, operation_detail,
        result, error_message, operator
    ) VALUES (
        p_device_ip, p_device_mac, p_operation_type, p_operation_detail,
        p_result, p_error_message, IFNULL(p_operator, 'system')
    );
END //
DELIMITER ;

-- 存储过程：更新设备信息
DELIMITER //
CREATE PROCEDURE UpdateDevice(
    IN p_ip_address VARCHAR(15),
    IN p_mac_address VARCHAR(17),
    IN p_device_name VARCHAR(255),
    IN p_vendor VARCHAR(255)
)
BEGIN
    INSERT INTO devices (ip_address, mac_address, device_name, vendor)
    VALUES (p_ip_address, p_mac_address, p_device_name, p_vendor)
    ON DUPLICATE KEY UPDATE
        device_name = IFNULL(p_device_name, device_name),
        vendor = IFNULL(p_vendor, vendor),
        last_seen = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- 存储过程：清理旧日志
DELIMITER //
CREATE PROCEDURE CleanOldLogs()
BEGIN
    DECLARE retention_days INT DEFAULT 30;
    
    -- 获取配置的保留天数
    SELECT CAST(config_value AS SIGNED) INTO retention_days
    FROM system_config 
    WHERE config_key = 'log_retention_days';
    
    -- 删除旧的操作日志
    DELETE FROM operation_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除旧的流量统计
    DELETE FROM traffic_stats 
    WHERE record_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除旧的扫描记录
    DELETE FROM scan_records 
    WHERE scan_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    SELECT ROW_COUNT() as deleted_rows;
END //
DELIMITER ;

-- 创建定时清理事件（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
CREATE EVENT IF NOT EXISTS cleanup_old_logs
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
    CALL CleanOldLogs();

-- 创建用户和权限（可选）
-- CREATE USER 'network_manager'@'localhost' IDENTIFIED BY 'your_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON network_manager.* TO 'network_manager'@'localhost';
-- FLUSH PRIVILEGES;

-- 插入示例数据（测试用）
-- INSERT INTO devices (ip_address, mac_address, device_name, vendor) VALUES
-- ('***********', '00:11:22:33:44:55', '路由器', 'TP-Link'),
-- ('*************', '00:11:22:33:44:66', '测试设备', '未知厂商');

-- 查询示例
-- SELECT * FROM active_devices;
-- SELECT * FROM blocked_devices;
-- SELECT * FROM operation_stats;
-- CALL LogOperation('*************', '00:11:22:33:44:66', 'block', '手动屏蔽', 'success', NULL, 'admin');
