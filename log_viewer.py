#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志查看器
用于查看和分析操作日志
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import csv
import os
from datetime import datetime, timedelta

class LogViewer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("日志查看器")
        self.root.geometry("1000x700")
        
        self.log_data = []
        self.filtered_data = []
        
        self.create_widgets()
        self.load_logs()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="5")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 刷新按钮
        ttk.Button(control_frame, text="刷新日志", command=self.load_logs).grid(row=0, column=0, padx=(0, 10))
        
        # 导出按钮
        ttk.Button(control_frame, text="导出CSV", command=self.export_csv).grid(row=0, column=1, padx=(0, 10))
        
        # 清空日志按钮
        ttk.Button(control_frame, text="清空日志", command=self.clear_logs).grid(row=0, column=2, padx=(0, 10))
        
        # 过滤器
        ttk.Label(control_frame, text="操作类型:").grid(row=0, column=3, padx=(20, 5))
        self.operation_var = tk.StringVar()
        operation_combo = ttk.Combobox(control_frame, textvariable=self.operation_var, width=10)
        operation_combo['values'] = ('全部', 'scan', 'block', 'unblock', 'limit_speed', 'shutdown')
        operation_combo.set('全部')
        operation_combo.grid(row=0, column=4, padx=(0, 10))
        operation_combo.bind('<<ComboboxSelected>>', self.filter_logs)
        
        # 日期过滤
        ttk.Label(control_frame, text="日期:").grid(row=0, column=5, padx=(10, 5))
        self.date_var = tk.StringVar()
        date_combo = ttk.Combobox(control_frame, textvariable=self.date_var, width=10)
        date_combo['values'] = ('全部', '今天', '昨天', '最近7天', '最近30天')
        date_combo.set('全部')
        date_combo.grid(row=0, column=6)
        date_combo.bind('<<ComboboxSelected>>', self.filter_logs)
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_frame, text="加载中...")
        self.stats_label.grid(row=0, column=0, sticky=tk.W)
        
        # 日志列表
        list_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建Treeview
        columns = ('时间', '操作类型', '设备IP', '设备名', '结果', '详情')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        column_widths = {'时间': 150, '操作类型': 100, '设备IP': 120, '设备名': 150, '结果': 80, '详情': 300}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100))
        
        # 滚动条
        scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 详细信息框架
        detail_frame = ttk.LabelFrame(main_frame, text="详细信息", padding="5")
        detail_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.detail_text = tk.Text(detail_frame, height=8, wrap=tk.WORD)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        detail_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(3, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        detail_frame.columnconfigure(0, weight=1)
        detail_frame.rowconfigure(0, weight=1)
    
    def load_logs(self):
        """加载日志数据"""
        self.log_data = []
        
        # 加载详细日志
        if os.path.exists('detailed_log.json'):
            try:
                with open('detailed_log.json', 'r', encoding='utf-8') as f:
                    self.log_data = json.load(f)
            except Exception as e:
                messagebox.showerror("错误", f"加载日志失败: {e}")
        
        # 如果没有详细日志，尝试解析文本日志
        if not self.log_data and os.path.exists('operation.log'):
            try:
                self.parse_text_log()
            except Exception as e:
                messagebox.showerror("错误", f"解析文本日志失败: {e}")
        
        self.filtered_data = self.log_data.copy()
        self.update_display()
        self.update_stats()
    
    def parse_text_log(self):
        """解析文本日志文件"""
        with open('operation.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            try:
                # 简单解析日志行
                if ' - INFO - ' in line:
                    parts = line.strip().split(' - INFO - ', 1)
                    if len(parts) == 2:
                        timestamp_str = parts[0]
                        message = parts[1]
                        
                        # 尝试解析时间戳
                        try:
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                        except:
                            timestamp = datetime.now()
                        
                        # 创建日志条目
                        log_entry = {
                            'timestamp': timestamp.isoformat(),
                            'operation_type': 'info',
                            'device_ip': '',
                            'device_mac': '',
                            'device_name': '',
                            'result': 'success',
                            'details': message
                        }
                        self.log_data.append(log_entry)
            except Exception:
                continue
    
    def filter_logs(self, event=None):
        """过滤日志数据"""
        operation_filter = self.operation_var.get()
        date_filter = self.date_var.get()
        
        self.filtered_data = []
        
        for log in self.log_data:
            # 操作类型过滤
            if operation_filter != '全部' and log.get('operation_type') != operation_filter:
                continue
            
            # 日期过滤
            if date_filter != '全部':
                try:
                    log_time = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                    now = datetime.now()
                    
                    if date_filter == '今天':
                        if log_time.date() != now.date():
                            continue
                    elif date_filter == '昨天':
                        yesterday = now - timedelta(days=1)
                        if log_time.date() != yesterday.date():
                            continue
                    elif date_filter == '最近7天':
                        if log_time < now - timedelta(days=7):
                            continue
                    elif date_filter == '最近30天':
                        if log_time < now - timedelta(days=30):
                            continue
                except:
                    continue
            
            self.filtered_data.append(log)
        
        self.update_display()
        self.update_stats()
    
    def update_display(self):
        """更新显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 按时间倒序排列
        sorted_data = sorted(self.filtered_data, 
                           key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # 添加数据到树形视图
        for log in sorted_data:
            try:
                timestamp = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                time_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            except:
                time_str = log.get('timestamp', '')
            
            self.tree.insert('', tk.END, values=(
                time_str,
                log.get('operation_type', ''),
                log.get('device_ip', ''),
                log.get('device_name', ''),
                log.get('result', ''),
                log.get('details', '')[:50] + '...' if len(log.get('details', '')) > 50 else log.get('details', '')
            ))
    
    def update_stats(self):
        """更新统计信息"""
        total = len(self.filtered_data)
        
        # 统计各种操作类型
        operation_counts = {}
        result_counts = {'success': 0, 'failed': 0, 'warning': 0}
        
        for log in self.filtered_data:
            op_type = log.get('operation_type', 'unknown')
            operation_counts[op_type] = operation_counts.get(op_type, 0) + 1
            
            result = log.get('result', 'unknown')
            if result in result_counts:
                result_counts[result] += 1
        
        # 构建统计文本
        stats_text = f"总计: {total} 条记录  |  "
        stats_text += "  |  ".join([f"{op}: {count}" for op, count in operation_counts.items()])
        stats_text += f"  |  成功: {result_counts['success']}  失败: {result_counts['failed']}  警告: {result_counts['warning']}"
        
        self.stats_label.config(text=stats_text)
    
    def on_select(self, event):
        """选择事件处理"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            index = self.tree.index(item)
            
            if index < len(self.filtered_data):
                log = sorted(self.filtered_data, 
                           key=lambda x: x.get('timestamp', ''), reverse=True)[index]
                
                # 显示详细信息
                detail_text = f"时间: {log.get('timestamp', '')}\n"
                detail_text += f"操作类型: {log.get('operation_type', '')}\n"
                detail_text += f"设备IP: {log.get('device_ip', '')}\n"
                detail_text += f"设备MAC: {log.get('device_mac', '')}\n"
                detail_text += f"设备名: {log.get('device_name', '')}\n"
                detail_text += f"结果: {log.get('result', '')}\n"
                detail_text += f"详情: {log.get('details', '')}\n"
                
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(1.0, detail_text)
    
    def export_csv(self):
        """导出CSV文件"""
        if not self.filtered_data:
            messagebox.showwarning("警告", "没有数据可导出")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['timestamp', 'operation_type', 'device_ip', 
                                'device_mac', 'device_name', 'result', 'details']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    for log in self.filtered_data:
                        writer.writerow(log)
                
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")
    
    def clear_logs(self):
        """清空日志"""
        result = messagebox.askyesno("确认", "确定要清空所有日志吗？此操作不可恢复！")
        if result:
            try:
                # 删除日志文件
                for filename in ['detailed_log.json', 'operation.log']:
                    if os.path.exists(filename):
                        os.remove(filename)
                
                self.log_data = []
                self.filtered_data = []
                self.update_display()
                self.update_stats()
                
                messagebox.showinfo("成功", "日志已清空")
            except Exception as e:
                messagebox.showerror("错误", f"清空日志失败: {e}")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LogViewer()
    app.run()
