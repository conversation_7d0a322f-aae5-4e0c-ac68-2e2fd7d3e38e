#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量控制模块
实现网速限制功能
"""

import subprocess
import platform
import time
import threading
from scapy.all import *
from collections import defaultdict
import psutil

class TrafficController:
    def __init__(self):
        self.system = platform.system()
        self.limited_devices = {}
        self.traffic_stats = defaultdict(lambda: {'rx': 0, 'tx': 0})
        self.monitoring = False
        
    def limit_device_speed(self, target_ip, download_limit_kbps, upload_limit_kbps):
        """限制设备网速"""
        if self.system == "Windows":
            return self._limit_speed_windows(target_ip, download_limit_kbps, upload_limit_kbps)
        elif self.system == "Linux":
            return self._limit_speed_linux(target_ip, download_limit_kbps, upload_limit_kbps)
        else:
            return False, "不支持的操作系统"
    
    def _limit_speed_windows(self, target_ip, download_limit, upload_limit):
        """Windows系统下的网速限制"""
        try:
            # 使用netsh命令进行流量控制
            # 注意：这需要管理员权限
            
            # 创建QoS策略
            cmd_create_policy = f'''
            netsh advfirewall firewall add rule name="Limit_{target_ip}_Download" 
            dir=in action=allow protocol=any remoteip={target_ip} 
            enable=yes profile=any
            '''
            
            # 实际的带宽限制需要使用Windows QoS或第三方工具
            # 这里提供一个基本框架
            
            self.limited_devices[target_ip] = {
                'download_limit': download_limit,
                'upload_limit': upload_limit,
                'active': True
            }
            
            # 启动流量监控和限制
            self._start_traffic_shaping(target_ip, download_limit, upload_limit)
            
            return True, f"已对 {target_ip} 应用速度限制"
            
        except Exception as e:
            return False, f"Windows流量控制失败: {str(e)}"
    
    def _limit_speed_linux(self, target_ip, download_limit, upload_limit):
        """Linux系统下的网速限制"""
        try:
            # 使用tc (traffic control) 命令
            interface = self._get_network_interface()
            
            # 创建根队列
            subprocess.run([
                'tc', 'qdisc', 'add', 'dev', interface, 'root', 'handle', '1:', 'htb'
            ], check=True)
            
            # 创建类别限制
            subprocess.run([
                'tc', 'class', 'add', 'dev', interface, 'parent', '1:', 
                'classid', f'1:{hash(target_ip) % 1000}', 'htb', 
                f'rate', f'{download_limit}kbit', f'ceil', f'{download_limit}kbit'
            ], check=True)
            
            # 添加过滤器
            subprocess.run([
                'tc', 'filter', 'add', 'dev', interface, 'protocol', 'ip',
                'parent', '1:0', 'prio', '1', 'u32', 
                'match', 'ip', 'dst', target_ip, 'flowid', f'1:{hash(target_ip) % 1000}'
            ], check=True)
            
            return True, f"已对 {target_ip} 应用Linux流量控制"
            
        except subprocess.CalledProcessError as e:
            return False, f"Linux流量控制失败: {str(e)}"
        except Exception as e:
            return False, f"流量控制错误: {str(e)}"
    
    def _get_network_interface(self):
        """获取主要网络接口"""
        try:
            # 获取默认路由的接口
            if self.system == "Linux":
                result = subprocess.run(['ip', 'route', 'show', 'default'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    parts = result.stdout.split()
                    if 'dev' in parts:
                        return parts[parts.index('dev') + 1]
            
            # 备用方法：使用psutil
            stats = psutil.net_if_stats()
            for interface, stat in stats.items():
                if stat.isup and not interface.startswith('lo'):
                    return interface
            
            return 'eth0'  # 默认值
            
        except Exception:
            return 'eth0'
    
    def _start_traffic_shaping(self, target_ip, download_limit, upload_limit):
        """启动流量整形"""
        def traffic_shaper():
            while target_ip in self.limited_devices and self.limited_devices[target_ip]['active']:
                try:
                    # 监控和限制流量
                    self._monitor_and_limit_traffic(target_ip, download_limit, upload_limit)
                    time.sleep(0.1)  # 100ms检查间隔
                except Exception as e:
                    print(f"流量整形错误: {e}")
                    break
        
        thread = threading.Thread(target=traffic_shaper, daemon=True)
        thread.start()
    
    def _monitor_and_limit_traffic(self, target_ip, download_limit, upload_limit):
        """监控和限制流量"""
        # 这是一个简化的实现
        # 实际应用中需要更复杂的包过滤和速率控制
        
        # 获取网络统计信息
        current_stats = self._get_network_stats(target_ip)
        
        if current_stats:
            # 计算当前速率
            rx_rate = current_stats['rx'] - self.traffic_stats[target_ip]['rx']
            tx_rate = current_stats['tx'] - self.traffic_stats[target_ip]['tx']
            
            # 更新统计信息
            self.traffic_stats[target_ip] = current_stats
            
            # 如果超过限制，可以采取措施（如丢包、延迟等）
            if rx_rate > download_limit * 1024:  # 转换为字节
                self._apply_rate_limit(target_ip, 'download')
            
            if tx_rate > upload_limit * 1024:
                self._apply_rate_limit(target_ip, 'upload')
    
    def _get_network_stats(self, target_ip):
        """获取指定IP的网络统计信息"""
        try:
            # 这里需要实现获取特定IP流量统计的逻辑
            # 可以使用netstat、ss命令或者直接解析/proc/net/文件
            return {'rx': 0, 'tx': 0}  # 简化实现
        except Exception:
            return None
    
    def _apply_rate_limit(self, target_ip, direction):
        """应用速率限制"""
        # 这里可以实现具体的限速措施
        # 例如：暂停转发、增加延迟、丢弃包等
        pass
    
    def remove_limit(self, target_ip):
        """移除对设备的速度限制"""
        if target_ip in self.limited_devices:
            self.limited_devices[target_ip]['active'] = False
            del self.limited_devices[target_ip]
            
            if self.system == "Linux":
                try:
                    interface = self._get_network_interface()
                    # 删除tc规则
                    subprocess.run([
                        'tc', 'filter', 'del', 'dev', interface, 'protocol', 'ip',
                        'parent', '1:0', 'prio', '1'
                    ], check=False)
                except Exception as e:
                    print(f"删除Linux流量控制规则失败: {e}")
            
            return True, f"已移除 {target_ip} 的速度限制"
        
        return False, f"{target_ip} 没有被限制"
    
    def get_traffic_stats(self, target_ip):
        """获取流量统计信息"""
        if target_ip in self.traffic_stats:
            return self.traffic_stats[target_ip]
        return {'rx': 0, 'tx': 0}
    
    def list_limited_devices(self):
        """列出所有被限制的设备"""
        return dict(self.limited_devices)
    
    def start_monitoring(self):
        """开始流量监控"""
        if not self.monitoring:
            self.monitoring = True
            threading.Thread(target=self._monitor_loop, daemon=True).start()
    
    def stop_monitoring(self):
        """停止流量监控"""
        self.monitoring = False
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 更新所有设备的流量统计
                for target_ip in list(self.limited_devices.keys()):
                    if self.limited_devices[target_ip]['active']:
                        self._monitor_and_limit_traffic(
                            target_ip,
                            self.limited_devices[target_ip]['download_limit'],
                            self.limited_devices[target_ip]['upload_limit']
                        )
                
                time.sleep(1)  # 每秒更新一次
                
            except Exception as e:
                print(f"监控循环错误: {e}")
                time.sleep(5)

# 使用示例
if __name__ == "__main__":
    tc = TrafficController()
    
    # 限制设备速度
    success, message = tc.limit_device_speed("*************", 100, 50)
    print(f"限速结果: {message}")
    
    # 开始监控
    tc.start_monitoring()
    
    try:
        time.sleep(60)  # 运行1分钟
    except KeyboardInterrupt:
        pass
    
    # 移除限制
    success, message = tc.remove_limit("*************")
    print(f"移除限制: {message}")
    
    tc.stop_monitoring()
