@echo off
chcp 65001 >nul
echo Network Device Manager Startup Script
echo =======================================
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Admin privileges detected, starting...
) else (
    echo WARNING: No admin privileges detected
    echo Some features may not work properly
    echo Please run as administrator
    echo.
    pause
)

REM Check Python installation
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo Python is installed
    python --version
) else (
    echo ERROR: Python not found, please install Python 3.7+
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check dependencies
echo Checking dependencies...
pip show scapy >nul 2>&1
if %errorLevel% == 0 (
    echo Dependencies are installed
) else (
    echo Installing dependencies...
    pip install scapy netifaces psutil
    if %errorLevel% neq 0 (
        echo Dependency installation failed
        echo Please run manually: pip install scapy netifaces psutil
        pause
        exit /b 1
    )
)

echo.
echo Please select program to start:
echo 1. Main Program (Network Manager)
echo 2. Log Viewer
echo 3. Exit
echo.
set /p choice=Enter your choice (1-3):

if "%choice%"=="1" (
    echo Starting main program...
    python network_manager.py
) else if "%choice%"=="2" (
    echo Starting log viewer...
    python log_viewer.py
) else if "%choice%"=="3" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, starting main program...
    python network_manager.py
)

pause
