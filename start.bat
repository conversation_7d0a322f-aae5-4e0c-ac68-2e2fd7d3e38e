@echo off
echo 局域网设备管理工具启动脚本
echo ================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，正在启动...
) else (
    echo 警告: 未检测到管理员权限
    echo 某些功能可能无法正常使用
    echo 建议右键选择"以管理员身份运行"
    echo.
    pause
)

REM 检查Python是否安装
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo Python已安装
) else (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show scapy >nul 2>&1
if %errorLevel% == 0 (
    echo 依赖包已安装
) else (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if %errorLevel% neq 0 (
        echo 依赖包安装失败，请手动执行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo.
echo 请选择要启动的程序:
echo 1. 主程序 (网络管理工具)
echo 2. 日志查看器
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3):

if "%choice%"=="1" (
    echo 启动主程序...
    python network_manager.py
) else if "%choice%"=="2" (
    echo 启动日志查看器...
    python log_viewer.py
) else if "%choice%"=="3" (
    echo 退出...
    exit /b 0
) else (
    echo 无效选择，启动主程序...
    python network_manager.py
)

pause
