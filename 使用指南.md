# 局域网设备管理工具 - 完整使用指南

## 📋 目录
1. [功能概述](#功能概述)
2. [安装步骤](#安装步骤)
3. [数据库配置](#数据库配置)
4. [使用方法](#使用方法)
5. [高级功能](#高级功能)
6. [故障排除](#故障排除)
7. [安全注意事项](#安全注意事项)

## 🎯 功能概述

### 核心功能
- **设备扫描**: 自动发现局域网内所有设备
- **设备屏蔽**: 使用ARP欺骗技术断开设备网络连接
- **网速限制**: 限制指定设备的上传下载速度
- **实时监控**: 显示设备状态和网络活动
- **数据记录**: 完整的操作日志和设备历史

### 技术特点
- 基于Python开发，跨平台支持
- 图形化界面，操作简单直观
- MySQL数据库存储，数据持久化
- 多线程处理，性能优异
- 详细日志记录，便于审计

## 🚀 安装步骤

### 1. 环境准备
```bash
# 确保Python 3.7+已安装
python --version

# 确保MySQL已安装并运行
mysql --version
```

### 2. 下载项目
```bash
git clone <repository-url>
cd wifi-manager
```

### 3. 自动安装
```bash
# 运行安装脚本
python install.py
```

### 4. 手动安装依赖
```bash
# 如果自动安装失败，手动安装
pip install -r requirements.txt
```

## 🗄️ 数据库配置

### 1. 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 执行数据库脚本
source database.sql
```

### 2. 配置连接
编辑 `database_manager.py` 中的连接参数：
```python
db = DatabaseManager(
    host='localhost',      # 数据库主机
    database='network_manager',  # 数据库名
    user='root',          # 用户名
    password='your_password',    # 密码
    port=3306            # 端口
)
```

### 3. 测试连接
```bash
python database_manager.py
```

## 📖 使用方法

### 启动程序
1. **Windows用户**:
   - 右键点击 `start.bat`
   - 选择 "以管理员身份运行"

2. **命令行启动**:
   ```bash
   python network_manager.py
   ```

### 基本操作流程

#### 1. 扫描设备
- 点击 "扫描设备" 按钮
- 等待扫描完成
- 查看设备列表中的发现结果

#### 2. 屏蔽设备
- 在设备列表中选择目标设备
- 点击 "屏蔽选中设备"
- 观察设备状态变为 "已屏蔽"
- 目标设备将无法访问网络

#### 3. 解除屏蔽
- 选择已屏蔽的设备
- 点击 "解除屏蔽"
- 设备网络访问恢复正常

#### 4. 限制网速
- 选择要限制的设备
- 点击 "限制网速"
- 在弹出窗口中设置:
  - 下载速度限制 (KB/s)
  - 上传速度限制 (KB/s)
- 点击 "应用" 确认

### 界面说明

#### 网络信息区域
- 显示本机IP地址
- 显示网关地址
- 显示当前网络段

#### 设备列表
| 列名 | 说明 |
|------|------|
| IP | 设备IP地址 |
| MAC | 设备MAC地址 |
| 设备名 | 设备主机名 |
| 状态 | 正常/已屏蔽/限速 |
| 厂商 | 网卡厂商信息 |

#### 操作日志
- 实时显示所有操作记录
- 包含时间戳和详细信息
- 便于问题排查和审计

## 🔧 高级功能

### 数据库查询
```sql
-- 查看活跃设备
SELECT * FROM active_devices;

-- 查看屏蔽历史
SELECT * FROM blocked_devices;

-- 查看操作统计
SELECT * FROM operation_stats;

-- 查看流量统计
SELECT * FROM traffic_stats WHERE device_ip = '*************';
```

### 配置管理
```sql
-- 修改扫描超时时间
UPDATE system_config SET config_value = '5' WHERE config_key = 'scan_timeout';

-- 修改ARP欺骗间隔
UPDATE system_config SET config_value = '1' WHERE config_key = 'arp_interval';

-- 设置日志保留天数
UPDATE system_config SET config_value = '60' WHERE config_key = 'log_retention_days';
```

### 批量操作
```python
# 批量屏蔽设备
blocked_ips = ['*************', '*************', '*************']
for ip in blocked_ips:
    db.set_device_blocked(ip, True)

# 批量设置限速
for ip in blocked_ips:
    db.set_device_speed_limit(ip, 100, 50)  # 100KB/s下载, 50KB/s上传
```

## 🔍 故障排除

### 常见问题

#### 1. 程序无法启动
**症状**: 双击程序无反应或报错
**解决方案**:
- 确保以管理员身份运行
- 检查Python版本 (需要3.7+)
- 验证所有依赖包已安装
- 查看错误日志

#### 2. 扫描不到设备
**症状**: 点击扫描后设备列表为空
**解决方案**:
- 检查网络连接状态
- 确认防火墙设置
- 验证网络段配置
- 尝试手动指定网络段

#### 3. 屏蔽功能无效
**症状**: 设备显示已屏蔽但仍能上网
**解决方案**:
- 确保管理员权限
- 检查目标设备ARP表
- 验证网关MAC地址
- 重启网络适配器

#### 4. 数据库连接失败
**症状**: 程序启动时数据库错误
**解决方案**:
- 检查MySQL服务状态
- 验证连接参数
- 确认数据库权限
- 检查防火墙端口

### 日志分析
```bash
# 查看程序日志
tail -f network_manager.log

# 查看数据库日志
tail -f /var/log/mysql/error.log
```

### 性能优化
1. **扫描优化**:
   - 减少扫描超时时间
   - 限制扫描范围
   - 使用多线程扫描

2. **数据库优化**:
   - 定期清理旧日志
   - 添加适当索引
   - 优化查询语句

3. **内存优化**:
   - 限制同时屏蔽设备数
   - 定期清理内存缓存
   - 优化线程管理

## ⚠️ 安全注意事项

### 法律合规
- **仅限合法使用**: 只能在自己管理的网络中使用
- **获得授权**: 使用前确保有相应的网络管理权限
- **遵守法律**: 不得用于非法入侵或攻击他人网络

### 技术安全
- **管理员权限**: 程序需要管理员权限才能正常工作
- **网络影响**: ARP欺骗可能影响网络稳定性
- **数据安全**: 数据库包含敏感的网络信息，注意保护

### 使用建议
- **测试环境**: 首次使用建议在测试环境中验证
- **备份配置**: 定期备份数据库和配置文件
- **监控日志**: 定期检查操作日志，发现异常及时处理
- **更新维护**: 定期更新程序和依赖包

### 应急处理
如果程序出现问题导致网络异常：
1. 立即停止程序运行
2. 重启网络设备
3. 清除ARP缓存: `arp -d *`
4. 重启网络适配器

## 📞 技术支持

如遇到问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误信息截图
- 操作日志
- 网络环境描述

---

**免责声明**: 本工具仅供学习和合法的网络管理使用。使用者应当遵守当地法律法规，不得用于非法目的。开发者不承担任何因使用本工具而产生的法律责任。
