@echo off
title Network Device Manager

echo ========================================
echo    Network Device Manager
echo ========================================
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo WARNING: Not running as administrator
    echo Some features may not work properly
    echo Please right-click and "Run as administrator"
    echo.
    timeout /t 3 >nul
)

REM Test imports first
echo Testing required modules...
python test_imports.py
if %errorLevel% neq 0 (
    echo.
    echo ERROR: Some required modules are missing
    echo Please run: pip install scapy psutil
    echo.
    pause
    exit /b 1
)

echo.
echo Starting Network Device Manager...
echo.

REM Start the main program
python network_manager.py

REM If we get here, the program has exited
echo.
echo Program has exited.
pause
