#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装检查脚本
检查Python环境和依赖包
"""

import sys
import subprocess
import importlib
import platform

def check_python_version():
    """检查Python版本"""
    print("=" * 50)
    print("检查Python环境")
    print("=" * 50)
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    print(f"系统平台: {platform.system()} {platform.release()}")
    print(f"架构: {platform.architecture()[0]}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_pip():
    """检查pip是否可用"""
    try:
        import pip
        print("✅ pip已安装")
        
        # 检查pip版本
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"pip版本: {result.stdout.strip()}")
        return True
    except ImportError:
        print("❌ pip未安装")
        return False

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {e}")
        return False

def check_package(package_name, install_name=None):
    """检查并安装包"""
    if install_name is None:
        install_name = package_name
    
    try:
        importlib.import_module(package_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        
        # 尝试安装
        choice = input(f"是否安装 {package_name}? (y/n): ").lower()
        if choice in ['y', 'yes', '是']:
            return install_package(install_name)
        else:
            return False

def check_admin_privileges():
    """检查管理员权限"""
    try:
        if platform.system() == "Windows":
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if is_admin:
                print("✅ 检测到管理员权限")
            else:
                print("⚠️  未检测到管理员权限")
                print("   建议以管理员身份运行以获得完整功能")
            return is_admin
        else:
            import os
            if os.geteuid() == 0:
                print("✅ 检测到root权限")
                return True
            else:
                print("⚠️  未检测到root权限")
                print("   某些功能可能需要sudo权限")
                return False
    except Exception as e:
        print(f"权限检查失败: {e}")
        return False

def test_network_access():
    """测试网络访问"""
    try:
        import socket
        socket.create_connection(("*******", 53), timeout=3)
        print("✅ 网络连接正常")
        return True
    except Exception:
        print("⚠️  网络连接可能有问题")
        return False

def main():
    """主函数"""
    print("网络设备管理工具 - 环境检查")
    print()
    
    # 检查Python版本
    if not check_python_version():
        print("\n请升级Python版本后重试")
        input("按回车键退出...")
        return False
    
    print()
    
    # 检查pip
    if not check_pip():
        print("\n请安装pip后重试")
        input("按回车键退出...")
        return False
    
    print()
    print("=" * 50)
    print("检查依赖包")
    print("=" * 50)
    
    # 检查依赖包
    packages = [
        ("scapy", "scapy==2.5.0"),
        ("netifaces", "netifaces==0.11.0"),
        ("psutil", "psutil==5.9.8")
    ]
    
    all_installed = True
    for package, install_name in packages:
        if not check_package(package, install_name):
            all_installed = False
    
    print()
    print("=" * 50)
    print("系统检查")
    print("=" * 50)
    
    # 检查权限
    check_admin_privileges()
    
    # 检查网络
    test_network_access()
    
    print()
    print("=" * 50)
    print("检查结果")
    print("=" * 50)
    
    if all_installed:
        print("✅ 所有依赖包已安装，可以运行程序")
        print("\n启动方式:")
        print("1. 双击 start.bat (Windows)")
        print("2. 运行: python network_manager.py")
        print("3. 查看日志: python log_viewer.py")
    else:
        print("❌ 部分依赖包未安装")
        print("\n请手动安装缺失的包:")
        print("pip install scapy netifaces psutil")
    
    print()
    input("按回车键退出...")
    return all_installed

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n检查过程中出现错误: {e}")
        input("按回车键退出...")
